{"object": "list", "results": [{"object": "page", "id": "150b9836-6aec-8090-bfc6-d59b8b27a05d", "created_time": "2024-12-02T08:51:00.000Z", "last_edited_time": "2025-08-02T15:22:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://www.notion.so/images/page-cover/gradients_11.jpg"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/currency_red.svg"}}, "parent": {"type": "workspace", "workspace": true}, "archived": false, "in_trash": false, "properties": {"title": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Airdrop Manager", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Airdrop Manager", "href": null}]}}, "url": "https://www.notion.so/Airdrop-Manager-150b98366aec8090bfc6d59b8b27a05d", "public_url": null}, {"object": "database", "id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd", "cover": null, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/hot-air-balloon_red.svg"}}, "created_time": "2024-12-02T08:51:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_time": "2025-08-01T13:10:00.000Z", "title": [{"type": "text", "text": {"content": "Airdrop Database", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Airdrop Database", "href": null}], "description": [], "is_inline": true, "properties": {"Category": {"id": "Ab~n", "name": "Category", "type": "multi_select", "multi_select": {"options": [{"id": "hnag", "name": "AI", "color": "orange", "description": null}, {"id": "iFrS", "name": "Bridge", "color": "purple", "description": null}, {"id": "sSMs", "name": "Cross-Chain", "color": "brown", "description": null}, {"id": "14b520ac-d9e7-4eb8-a97f-e66ba544c545", "name": "Cybersecurity", "color": "yellow", "description": null}, {"id": "@]IJ", "name": "<PERSON><PERSON><PERSON>", "color": "yellow", "description": null}, {"id": "\\]PA", "name": "<PERSON><PERSON><PERSON>", "color": "purple", "description": null}, {"id": ";Pcn", "name": "DEX", "color": "green", "description": null}, {"id": "isPO", "name": "GameFi", "color": "blue", "description": null}, {"id": "qhQ;", "name": "GPU Cloud", "color": "yellow", "description": null}, {"id": "?SI<", "name": "Layer 1", "color": "gray", "description": null}, {"id": "ZcSn", "name": "Layer 2", "color": "pink", "description": null}, {"id": "rjYS", "name": "MEME", "color": "red", "description": null}, {"id": "de4b485d-f160-4bf0-9c06-e4996f2845d6", "name": "MyFav", "color": "orange", "description": null}, {"id": "fU~z", "name": "NFT", "color": "default", "description": null}, {"id": "216a3ba7-033a-4eb9-a33d-ee113a97c6f0", "name": "Node", "color": "orange", "description": null}, {"id": "Y^fo", "name": "Other", "color": "blue", "description": null}, {"id": "d<Bk", "name": "RWA", "color": "red", "description": null}, {"id": "YGfY", "name": "StableCoin", "color": "green", "description": null}, {"id": "Z\\}[", "name": "Web3 Social", "color": "purple", "description": null}, {"id": ":VS[", "name": "Yield", "color": "green", "description": null}]}}, "Chain/Tech": {"id": "CY%5E%3E", "name": "Chain/Tech", "type": "select", "select": {"options": [{"id": "`Oit", "name": "Etherium", "color": "purple", "description": null}, {"id": "~\\;s", "name": "Solana", "color": "green", "description": null}, {"id": "rwiO", "name": "BNB Chain", "color": "orange", "description": null}, {"id": "HL^c", "name": "Polygon", "color": "yellow", "description": null}, {"id": "u~ok", "name": "SEI", "color": "green", "description": null}, {"id": "zON{", "name": "zkSync", "color": "brown", "description": null}, {"id": "If{u", "name": "Manta", "color": "blue", "description": null}, {"id": "OFUC", "name": "Bitcoin", "color": "yellow", "description": null}, {"id": "mDkj", "name": "Injective", "color": "pink", "description": null}, {"id": "zQaz", "name": "Stark<PERSON>", "color": "red", "description": null}, {"id": "@ISI", "name": "Optimism", "color": "default", "description": null}, {"id": "W^m@", "name": "Arbitrum", "color": "gray", "description": null}, {"id": "P^\\l", "name": "Cosmos", "color": "purple", "description": null}, {"id": "d6b5e2b9-3c54-493b-8cce-7da0586577db", "name": "EVM", "color": "red", "description": null}, {"id": "fd775711-a54d-4972-b1e2-696650279e05", "name": "SUI", "color": "orange", "description": null}]}}, "Multiple Account": {"id": "E%3EaB", "name": "Multiple Account", "type": "select", "select": {"options": [{"id": "CHS<", "name": "NO", "color": "red", "description": null}, {"id": "qEVf", "name": "YES", "color": "orange", "description": null}, {"id": "e80f02f1-2016-4112-bc10-288292ef32d1", "name": "Not started", "color": "brown", "description": null}, {"id": "a8639bc3-7a8d-4807-88ab-1750ce85db3c", "name": "In progress", "color": "blue", "description": null}, {"id": "1bcfb002-e62b-40f5-a268-1fb4ff83d23e", "name": "Done", "color": "green", "description": null}]}}, "Reward Landed": {"id": "Ey%7Dz", "name": "<PERSON><PERSON>ed", "type": "number", "number": {"format": "dollar"}}, "Usefull LInks": {"id": "IhWc", "name": "Usefull LInks", "type": "rich_text", "rich_text": {}}, "Social": {"id": "JII_", "name": "Social", "type": "url", "url": {}}, "Airdrop Status": {"id": "RbW_", "name": "Airdrop Status", "type": "select", "select": {"options": [{"id": "?dJ[", "name": "Confirmed", "color": "green", "description": null}, {"id": "\\[ZA", "name": "<PERSON><PERSON> Confirmed", "color": "default", "description": null}, {"id": "^e:<", "name": "Teased by Team", "color": "yellow", "description": null}, {"id": "`d@w", "name": "Unconfirmed", "color": "pink", "description": null}]}}, "Cost": {"id": "ZGZm", "name": "Cost", "type": "multi_select", "multi_select": {"options": [{"id": "xryQ", "name": "Free", "color": "green", "description": null}, {"id": "iL{I", "name": "Gas Fee", "color": "blue", "description": null}, {"id": "sm@`", "name": "Liquidity", "color": "red", "description": null}, {"id": "1634969d-c4f1-409c-910c-567a11bf4af0", "name": "Enetry Cost", "color": "pink", "description": null}]}}, "Progress": {"id": "%60aWd", "name": "Progress", "type": "status", "status": {"options": [{"id": "xi\\E", "name": "Cancelled", "color": "red", "description": null}, {"id": "79755ebf-b87f-4f13-95d1-b2e0ce8743a1", "name": "Not started", "color": "default", "description": null}, {"id": "a177129f-711e-4005-a755-46a843aaba8e", "name": "In progress", "color": "blue", "description": null}, {"id": "904b3dc7-90b3-4360-9305-d5c8e450d831", "name": "Done", "color": "green", "description": null}], "groups": [{"id": "38dc2daa-7940-4ed2-ab77-c7b7e76e4044", "name": "To-do", "color": "gray", "option_ids": ["79755ebf-b87f-4f13-95d1-b2e0ce8743a1"]}, {"id": "19d39f7e-f9ef-447c-80ee-ab6dd4bf7f97", "name": "In progress", "color": "blue", "option_ids": ["a177129f-711e-4005-a755-46a843aaba8e"]}, {"id": "ff3a257b-dac9-4907-ab4b-cace3fb9ab3a", "name": "Complete", "color": "green", "option_ids": ["xi\\E", "904b3dc7-90b3-4360-9305-d5c8e450d831"]}]}}, "Raised ($M)": {"id": "a%5EJU", "name": "Raised ($M)", "type": "rich_text", "rich_text": {}}, "Potential": {"id": "bQRn", "name": "Potential", "type": "select", "select": {"options": [{"id": "tN<e", "name": "Tier S", "color": "green", "description": null}, {"id": ":mXq", "name": "Tier A", "color": "purple", "description": null}, {"id": "k>tJ", "name": "Tier B", "color": "yellow", "description": null}, {"id": "z=}_", "name": "Tier C", "color": "red", "description": null}, {"id": "Y?rG", "name": "Tier D", "color": "gray", "description": null}]}}, "Stage": {"id": "bpjB", "name": "Stage", "type": "select", "select": {"options": [{"id": "Rd|C", "name": "Mainnet", "color": "blue", "description": null}, {"id": "rKWC", "name": "Testnet", "color": "red", "description": null}, {"id": "jICo", "name": "Alpha", "color": "yellow", "description": null}]}}, "Website": {"id": "fXM%60", "name": "Website", "type": "url", "url": {}}, "Date": {"id": "gpRK", "name": "Date", "type": "date", "date": {}}, "Name": {"id": "title", "name": "Name", "type": "title", "title": {}}}, "parent": {"type": "block_id", "block_id": "1c9b9836-6aec-8050-b4f8-c3fd0a643594"}, "url": "https://www.notion.so/150b98366aec81edb6a0c7e8e84468bd", "public_url": null, "archived": false, "in_trash": false}, {"object": "page", "id": "150b9836-6aec-80cb-9695-f5bcb34d1dce", "created_time": "2024-12-02T12:34:00.000Z", "last_edited_time": "2025-02-17T16:03:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://pbs.twimg.com/media/GaUUc_xa8AAhlcf.jpg:large"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/hot-air-balloon_gray.svg"}}, "parent": {"type": "database_id", "database_id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd"}, "archived": false, "in_trash": false, "properties": {"Category": {"id": "Ab~n", "type": "multi_select", "multi_select": [{"id": "ZcSn", "name": "Layer 2", "color": "pink"}]}, "Chain/Tech": {"id": "CY%5E%3E", "type": "select", "select": {"id": "~\\;s", "name": "Solana", "color": "green"}}, "Multiple Account": {"id": "E%3EaB", "type": "select", "select": null}, "Reward Landed": {"id": "Ey%7Dz", "type": "number", "number": null}, "Usefull LInks": {"id": "IhWc", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "https://t.me/sageairdrops/7400", "link": {"url": "https://t.me/sageairdrops/7400"}}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "https://t.me/sageairdrops/7400", "href": "https://t.me/sageairdrops/7400"}]}, "Social": {"id": "JII_", "type": "url", "url": null}, "Airdrop Status": {"id": "RbW_", "type": "select", "select": {"id": "`d@w", "name": "Unconfirmed", "color": "pink"}}, "Cost": {"id": "ZGZm", "type": "multi_select", "multi_select": [{"id": "xryQ", "name": "Free", "color": "green"}]}, "Progress": {"id": "%60aWd", "type": "status", "status": {"id": "904b3dc7-90b3-4360-9305-d5c8e450d831", "name": "Done", "color": "green"}}, "Tasks": {"id": "a%5Cad", "type": "relation", "relation": [], "has_more": false}, "Raised ($M)": {"id": "a%5EJU", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "39", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "39", "href": null}]}, "Potential": {"id": "bQRn", "type": "select", "select": {"id": ":mXq", "name": "Tier A", "color": "purple"}}, "Stage": {"id": "bpjB", "type": "select", "select": {"id": "jICo", "name": "Alpha", "color": "yellow"}}, "Website": {"id": "fXM%60", "type": "url", "url": "https://faucet.mantis.app/"}, "Date": {"id": "gpRK", "type": "date", "date": null}, "Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": " Mantis Airdrop", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": " Mantis Airdrop", "href": null}]}}, "url": "https://www.notion.so/Mantis-Airdrop-150b98366aec80cb9695f5bcb34d1dce", "public_url": null}, {"object": "page", "id": "150b9836-6aec-8047-814f-c6296a76002a", "created_time": "2024-12-02T12:20:00.000Z", "last_edited_time": "2025-02-17T15:50:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://pbs.twimg.com/profile_banners/1806389563788587009/1733344918/1080x360"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/hot-air-balloon_gray.svg"}}, "parent": {"type": "database_id", "database_id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd"}, "archived": false, "in_trash": false, "properties": {"Category": {"id": "Ab~n", "type": "multi_select", "multi_select": [{"id": "ZcSn", "name": "Layer 2", "color": "pink"}]}, "Chain/Tech": {"id": "CY%5E%3E", "type": "select", "select": {"id": "d6b5e2b9-3c54-493b-8cce-7da0586577db", "name": "EVM", "color": "red"}}, "Multiple Account": {"id": "E%3EaB", "type": "select", "select": null}, "Reward Landed": {"id": "Ey%7Dz", "type": "number", "number": null}, "Usefull LInks": {"id": "IhWc", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "https://abstract.deform.cc/?referral=EKVa6gbjh9Mj", "link": {"url": "https://abstract.deform.cc/?referral=EKVa6gbjh9Mj"}}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "https://abstract.deform.cc/?referral=EKVa6gbjh9Mj", "href": "https://abstract.deform.cc/?referral=EKVa6gbjh9Mj"}]}, "Social": {"id": "JII_", "type": "url", "url": null}, "Airdrop Status": {"id": "RbW_", "type": "select", "select": {"id": "^e:<", "name": "Teased by Team", "color": "yellow"}}, "Cost": {"id": "ZGZm", "type": "multi_select", "multi_select": [{"id": "xryQ", "name": "Free", "color": "green"}]}, "Progress": {"id": "%60aWd", "type": "status", "status": {"id": "a177129f-711e-4005-a755-46a843aaba8e", "name": "In progress", "color": "blue"}}, "Tasks": {"id": "a%5Cad", "type": "relation", "relation": [], "has_more": false}, "Raised ($M)": {"id": "a%5EJU", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "20", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "20", "href": null}]}, "Potential": {"id": "bQRn", "type": "select", "select": {"id": ":mXq", "name": "Tier A", "color": "purple"}}, "Stage": {"id": "bpjB", "type": "select", "select": {"id": "jICo", "name": "Alpha", "color": "yellow"}}, "Website": {"id": "fXM%60", "type": "url", "url": "https://abs.xyz/"}, "Date": {"id": "gpRK", "type": "date", "date": null}, "Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Abstract Airdrop", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Abstract Airdrop", "href": null}]}}, "url": "https://www.notion.so/Abstract-Airdrop-150b98366aec8047814fc6296a76002a", "public_url": null}, {"object": "page", "id": "150b9836-6aec-80eb-b43f-f294dbf53f54", "created_time": "2024-12-02T13:14:00.000Z", "last_edited_time": "2024-12-07T10:18:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://tiendientu.com/wp-content/uploads/2024/11/<PERSON><PERSON>-<PERSON>-<PERSON>-<PERSON>ia-Portal-Airdrop.webp"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/hot-air-balloon_gray.svg"}}, "parent": {"type": "database_id", "database_id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd"}, "archived": false, "in_trash": false, "properties": {"Category": {"id": "Ab~n", "type": "multi_select", "multi_select": [{"id": "Y^fo", "name": "Other", "color": "blue"}]}, "Chain/Tech": {"id": "CY%5E%3E", "type": "select", "select": {"id": "OFUC", "name": "Bitcoin", "color": "yellow"}}, "Multiple Account": {"id": "E%3EaB", "type": "select", "select": null}, "Reward Landed": {"id": "Ey%7Dz", "type": "number", "number": null}, "Usefull LInks": {"id": "IhWc", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "http://citrea.xyz/faucet", "link": {"url": "http://citrea.xyz/faucet"}}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "http://citrea.xyz/faucet", "href": "http://citrea.xyz/faucet"}, {"type": "text", "text": {"content": "\n", "link": null}, "annotations": {"bold": true, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "\n", "href": null}, {"type": "text", "text": {"content": "https://www.citrusswap.xyz/#/swap", "link": {"url": "https://www.citrusswap.xyz/#/swap"}}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "https://www.citrusswap.xyz/#/swap", "href": "https://www.citrusswap.xyz/#/swap"}]}, "Social": {"id": "JII_", "type": "url", "url": null}, "Airdrop Status": {"id": "RbW_", "type": "select", "select": {"id": "\\[ZA", "name": "<PERSON><PERSON> Confirmed", "color": "default"}}, "Cost": {"id": "ZGZm", "type": "multi_select", "multi_select": [{"id": "xryQ", "name": "Free", "color": "green"}]}, "Progress": {"id": "%60aWd", "type": "status", "status": {"id": "a177129f-711e-4005-a755-46a843aaba8e", "name": "In progress", "color": "blue"}}, "Tasks": {"id": "a%5Cad", "type": "relation", "relation": [], "has_more": false}, "Raised ($M)": {"id": "a%5EJU", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "42.5", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "42.5", "href": null}]}, "Potential": {"id": "bQRn", "type": "select", "select": {"id": ":mXq", "name": "Tier A", "color": "purple"}}, "Stage": {"id": "bpjB", "type": "select", "select": {"id": "jICo", "name": "Alpha", "color": "yellow"}}, "Website": {"id": "fXM%60", "type": "url", "url": "https://portaltobitcoin.bonusblock.io/"}, "Date": {"id": "gpRK", "type": "date", "date": null}, "Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Portal Airdrop", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Portal Airdrop", "href": null}]}}, "url": "https://www.notion.so/Portal-Airdrop-150b98366aec80ebb43ff294dbf53f54", "public_url": null}, {"object": "page", "id": "150b9836-6aec-8062-92c4-daaaebda8f37", "created_time": "2024-12-02T13:21:00.000Z", "last_edited_time": "2024-12-02T13:23:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSUPiKN7Gi_1M8AW5g01Jad2qwJUidS20vMTg&s"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/hot-air-balloon_gray.svg"}}, "parent": {"type": "database_id", "database_id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd"}, "archived": false, "in_trash": false, "properties": {"Category": {"id": "Ab~n", "type": "multi_select", "multi_select": [{"id": "ZcSn", "name": "Layer 2", "color": "pink"}]}, "Chain/Tech": {"id": "CY%5E%3E", "type": "select", "select": {"id": "OFUC", "name": "Bitcoin", "color": "yellow"}}, "Multiple Account": {"id": "E%3EaB", "type": "select", "select": null}, "Reward Landed": {"id": "Ey%7Dz", "type": "number", "number": null}, "Usefull LInks": {"id": "IhWc", "type": "rich_text", "rich_text": []}, "Social": {"id": "JII_", "type": "url", "url": null}, "Airdrop Status": {"id": "RbW_", "type": "select", "select": {"id": "^e:<", "name": "Teased by Team", "color": "yellow"}}, "Cost": {"id": "ZGZm", "type": "multi_select", "multi_select": [{"id": "xryQ", "name": "Free", "color": "green"}]}, "Progress": {"id": "%60aWd", "type": "status", "status": {"id": "a177129f-711e-4005-a755-46a843aaba8e", "name": "In progress", "color": "blue"}}, "Tasks": {"id": "a%5Cad", "type": "relation", "relation": [], "has_more": false}, "Raised ($M)": {"id": "a%5EJU", "type": "rich_text", "rich_text": [{"type": "text", "text": {"content": "16.7", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "16.7", "href": null}]}, "Potential": {"id": "bQRn", "type": "select", "select": {"id": ":mXq", "name": "Tier A", "color": "purple"}}, "Stage": {"id": "bpjB", "type": "select", "select": {"id": "rKWC", "name": "Testnet", "color": "red"}}, "Website": {"id": "fXM%60", "type": "url", "url": "https://citrea.xyz/"}, "Date": {"id": "gpRK", "type": "date", "date": null}, "Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Citrea Airdrop", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Citrea Airdrop", "href": null}]}}, "url": "https://www.notion.so/Citrea-Airdrop-150b98366aec806292c4daaaebda8f37", "public_url": null}, {"object": "page", "id": "a63da21d-2d86-47f5-b354-a1d85e1831eb", "created_time": "2024-08-04T09:15:00.000Z", "last_edited_time": "2024-08-04T11:52:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://www.notion.so/images/page-cover/nasa_the_blue_marble.jpg"}}, "icon": {"type": "emoji", "emoji": "🤑"}, "parent": {"type": "database_id", "database_id": "31075b59-203b-4760-995a-a8f4bac38f75"}, "archived": false, "in_trash": false, "properties": {"Date": {"id": "%3C%60FE", "type": "date", "date": {"start": "2024-08-04", "end": "2024-08-04", "time_zone": null}}, "GitHub Pull Requests": {"id": "LOfD", "type": "relation", "relation": [], "has_more": false}, "Completion": {"id": "NBIp", "type": "rollup", "rollup": {"type": "array", "array": [], "function": "show_original"}}, "Task": {"id": "%60L%40s", "type": "rich_text", "rich_text": []}, "Priority": {"id": "anWo", "type": "select", "select": {"id": "|ngG", "name": "High", "color": "red"}}, "Owner": {"id": "d%3A%3Cf", "type": "created_by", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocIMNc4PKOuwrf-cLBhKI-n_cDIEIJchSNh_P1umrt3r6zO2V_sH=s100", "type": "person", "person": {"email": "<EMAIL>"}}}, "Status": {"id": "sK%60P", "type": "status", "status": {"id": "[~Zy", "name": "Planing", "color": "blue"}}, "Due Date": {"id": "%7CbS%5D", "type": "date", "date": null}, "ID": {"id": "~Odm", "type": "unique_id", "unique_id": {"prefix": "PSL", "number": 3}}, "Project Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "A Crypto Trading Platform", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "A Crypto Trading Platform", "href": null}]}}, "url": "https://www.notion.so/A-Crypto-Trading-Platform-a63da21d2d8647f5b354a1d85e1831eb", "public_url": null}, {"object": "page", "id": "fed9d002-6d8d-4ef5-a397-a99d3ba27185", "created_time": "2024-08-04T09:16:00.000Z", "last_edited_time": "2024-08-04T09:44:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": null, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/pentagon-dashed_brown.svg"}}, "parent": {"type": "database_id", "database_id": "31075b59-203b-4760-995a-a8f4bac38f75"}, "archived": false, "in_trash": false, "properties": {"Date": {"id": "%3C%60FE", "type": "date", "date": null}, "GitHub Pull Requests": {"id": "LOfD", "type": "relation", "relation": [], "has_more": false}, "Completion": {"id": "NBIp", "type": "rollup", "rollup": {"type": "array", "array": [], "function": "show_original"}}, "Task": {"id": "%60L%40s", "type": "rich_text", "rich_text": []}, "Priority": {"id": "anWo", "type": "select", "select": {"id": "b_@Z", "name": "Low", "color": "green"}}, "Owner": {"id": "d%3A%3Cf", "type": "created_by", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocIMNc4PKOuwrf-cLBhKI-n_cDIEIJchSNh_P1umrt3r6zO2V_sH=s100", "type": "person", "person": {"email": "<EMAIL>"}}}, "Status": {"id": "sK%60P", "type": "status", "status": {"id": "43c184d8-220d-40ec-b30a-bad0f2ccef49", "name": "Not started", "color": "default"}}, "Due Date": {"id": "%7CbS%5D", "type": "date", "date": null}, "ID": {"id": "~Odm", "type": "unique_id", "unique_id": {"prefix": "PSL", "number": 6}}, "Project Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "You’re own Crypto token", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "You’re own Crypto token", "href": null}]}}, "url": "https://www.notion.so/You-re-own-Crypto-token-fed9d0026d8d4ef5a397a99d3ba27185", "public_url": null}, {"object": "page", "id": "be23d426-7bc7-4326-913d-1140abbb7152", "created_time": "2024-08-04T09:16:00.000Z", "last_edited_time": "2024-08-04T09:44:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": null, "icon": {"type": "emoji", "emoji": "🏦"}, "parent": {"type": "database_id", "database_id": "31075b59-203b-4760-995a-a8f4bac38f75"}, "archived": false, "in_trash": false, "properties": {"Date": {"id": "%3C%60FE", "type": "date", "date": null}, "GitHub Pull Requests": {"id": "LOfD", "type": "relation", "relation": [], "has_more": false}, "Completion": {"id": "NBIp", "type": "rollup", "rollup": {"type": "array", "array": [], "function": "show_original"}}, "Task": {"id": "%60L%40s", "type": "rich_text", "rich_text": []}, "Priority": {"id": "anWo", "type": "select", "select": {"id": "b_@Z", "name": "Low", "color": "green"}}, "Owner": {"id": "d%3A%3Cf", "type": "created_by", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocIMNc4PKOuwrf-cLBhKI-n_cDIEIJchSNh_P1umrt3r6zO2V_sH=s100", "type": "person", "person": {"email": "<EMAIL>"}}}, "Status": {"id": "sK%60P", "type": "status", "status": {"id": "43c184d8-220d-40ec-b30a-bad0f2ccef49", "name": "Not started", "color": "default"}}, "Due Date": {"id": "%7CbS%5D", "type": "date", "date": null}, "ID": {"id": "~Odm", "type": "unique_id", "unique_id": {"prefix": "PSL", "number": 7}}, "Project Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "A crypto Wallet", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "A crypto Wallet", "href": null}]}}, "url": "https://www.notion.so/A-crypto-Wallet-be23d4267bc74326913d1140abbb7152", "public_url": null}, {"object": "page", "id": "247d5d38-e7d0-465e-9d49-d5ae7f6e5333", "created_time": "2024-08-04T09:33:00.000Z", "last_edited_time": "2024-08-04T09:34:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": null, "icon": null, "parent": {"type": "database_id", "database_id": "31075b59-203b-4760-995a-a8f4bac38f75"}, "archived": false, "in_trash": false, "properties": {"Date": {"id": "%3C%60FE", "type": "date", "date": null}, "GitHub Pull Requests": {"id": "LOfD", "type": "relation", "relation": [], "has_more": false}, "Completion": {"id": "NBIp", "type": "rollup", "rollup": {"type": "array", "array": [], "function": "show_original"}}, "Task": {"id": "%60L%40s", "type": "rich_text", "rich_text": []}, "Priority": {"id": "anWo", "type": "select", "select": null}, "Owner": {"id": "d%3A%3Cf", "type": "created_by", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocIMNc4PKOuwrf-cLBhKI-n_cDIEIJchSNh_P1umrt3r6zO2V_sH=s100", "type": "person", "person": {"email": "<EMAIL>"}}}, "Status": {"id": "sK%60P", "type": "status", "status": {"id": "[~Zy", "name": "Planing", "color": "blue"}}, "Due Date": {"id": "%7CbS%5D", "type": "date", "date": null}, "ID": {"id": "~Odm", "type": "unique_id", "unique_id": {"prefix": null, "number": null}}, "Project Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Crypto Trading Platform", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Crypto Trading Platform", "href": null}]}}, "url": "https://www.notion.so/Crypto-Trading-Platform-247d5d38e7d0465e9d49d5ae7f6e5333", "public_url": null}], "next_cursor": null, "has_more": false, "type": "page_or_database", "page_or_database": {}, "request_id": "848d19cc-f148-4769-be45-71d3558a20c0"}